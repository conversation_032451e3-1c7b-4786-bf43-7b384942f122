<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易复盘工具 - 设计原型</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            
            --bg-primary: #0f0f23;
            --bg-secondary: #1a1a2e;
            --bg-card: rgba(255, 255, 255, 0.05);
            --bg-glass: rgba(255, 255, 255, 0.08);
            
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            
            --border-color: rgba(255, 255, 255, 0.1);
            --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-hover: 0 12px 48px rgba(0, 0, 0, 0.4);
            
            --profit-color: #00ff88;
            --loss-color: #ff4757;
            --neutral-color: #74b9ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .app-container {
            max-width: 1400px;
            margin: 20px auto;
            background: var(--bg-glass);
            border-radius: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .app-header {
            background: var(--primary-gradient);
            padding: 32px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        .app-header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .app-header p {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        .tab-container {
            border-bottom: 1px solid var(--border-color);
        }

        .tab-nav {
            display: flex;
            background: var(--bg-secondary);
            position: relative;
        }

        .tab-nav::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: calc(100% / 3);
            height: 3px;
            background: var(--primary-gradient);
            transition: transform 0.3s ease;
            transform: translateX(calc(var(--active-tab, 0) * 100%));
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            position: relative;
            font-family: inherit;
        }

        .tab-button.active {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.02);
        }

        .tab-button:hover:not(.active) {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.05);
        }

        .tab-content {
            display: none;
            padding: 40px;
            min-height: 700px;
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Quick Add 页面样式 */
        .quick-add-form {
            max-width: 700px;
            margin: 0 auto;
        }

        .form-section {
            margin-bottom: 32px;
            padding: 24px;
            background: var(--bg-card);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .form-section:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .form-section h3 {
            margin-bottom: 20px;
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .quick-button {
            padding: 12px 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .quick-button:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .quick-button.selected {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .radio-group {
            display: flex;
            gap: 24px;
            margin-bottom: 20px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            font-size: 16px;
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        .radio-option:hover {
            color: var(--text-primary);
        }

        .radio-option input[type="radio"] {
            width: 20px;
            height: 20px;
            accent-color: #667eea;
        }

        .custom-input {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 16px;
        }

        .custom-input label {
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 80px;
        }

        .custom-input input {
            padding: 12px 16px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 14px;
            color: var(--text-primary);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .custom-input input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .preview-section {
            background: var(--primary-gradient);
            padding: 32px;
            border-radius: 16px;
            margin: 32px 0;
            position: relative;
            overflow: hidden;
        }

        .preview-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 10% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .preview-section h3 {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            position: relative;
            z-index: 1;
        }

        .preview-calculations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .calc-item {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .calc-item:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.15);
        }

        .calc-item .label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .calc-item .value {
            font-size: 20px;
            font-weight: 700;
        }

        .notes-section textarea {
            width: 100%;
            padding: 16px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 14px;
            color: var(--text-primary);
            font-family: inherit;
            resize: vertical;
            min-height: 100px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .notes-section textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .notes-section textarea::placeholder {
            color: var(--text-muted);
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-family: inherit;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: var(--bg-card);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: var(--bg-glass);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-outline:hover {
            background: var(--bg-card);
            transform: translateY(-2px);
        }

        /* Trading Journal 页面样式 */
        .calendar-container {
            background: var(--bg-card);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 32px;
            box-shadow: var(--shadow-card);
        }

        .calendar-header {
            background: var(--primary-gradient);
            padding: 24px;
            text-align: center;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .calendar-header h3 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }

        .calendar-nav {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 12px 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .calendar-nav:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: var(--border-color);
            padding: 1px;
        }

        .calendar-weekday {
            background: var(--bg-secondary);
            padding: 16px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .calendar-day {
            background: var(--bg-card);
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .calendar-day:hover {
            background: var(--bg-glass);
            transform: scale(1.05);
        }

        .calendar-day.today {
            background: var(--primary-gradient);
            color: white;
            font-weight: bold;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }

        .calendar-day.profit {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.2), rgba(0, 255, 136, 0.1));
            color: var(--profit-color);
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .calendar-day.loss {
            background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 71, 87, 0.1));
            color: var(--loss-color);
            border: 1px solid rgba(255, 71, 87, 0.3);
        }

        .calendar-day.selected {
            background: var(--primary-gradient);
            color: white;
            font-weight: bold;
            box-shadow: 0 0 24px rgba(102, 126, 234, 0.6);
        }

        .calendar-day .day-number {
            font-size: 18px;
            margin-bottom: 4px;
            font-weight: 600;
        }

        .calendar-day .day-pnl {
            font-size: 11px;
            font-weight: 600;
            opacity: 0.9;
        }

        .calendar-day.other-month {
            background: var(--bg-secondary);
            color: var(--text-muted);
            opacity: 0.5;
        }

        .daily-summary {
            background: var(--bg-card);
            border-radius: 20px;
            padding: 32px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            margin-bottom: 24px;
            box-shadow: var(--shadow-card);
        }

        .daily-summary h3 {
            color: var(--text-primary);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .summary-stat {
            text-align: center;
            padding: 20px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .summary-stat:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-hover);
        }

        .summary-stat .label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .summary-stat .value {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .daily-trades {
            margin-top: 32px;
        }

        .daily-trades h4 {
            color: var(--text-primary);
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .daily-trade-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: var(--bg-glass);
            border-radius: 12px;
            margin-bottom: 12px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .daily-trade-item:hover {
            transform: translateX(8px);
            background: rgba(255, 255, 255, 0.1);
        }

        .daily-trade-item.profit-trade {
            border-left: 4px solid var(--profit-color);
        }

        .daily-trade-item.loss-trade {
            border-left: 4px solid var(--loss-color);
        }

        /* Data Analysis 页面样式 */
        .analysis-header {
            background: var(--bg-card);
            padding: 32px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            margin-bottom: 32px;
            box-shadow: var(--shadow-card);
        }

        .time-range-selector {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .range-button {
            padding: 12px 24px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .range-button:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
        }

        .range-button.selected {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .custom-range {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: 20px;
        }

        .custom-range input {
            padding: 10px 16px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 13px;
            color: var(--text-primary);
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .custom-range input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .core-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .metric-card {
            background: var(--bg-card);
            padding: 28px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-card);
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-hover);
        }

        .metric-card h4 {
            color: var(--text-secondary);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .metric-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .chart-section {
            background: var(--bg-card);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            margin-bottom: 32px;
            box-shadow: var(--shadow-card);
        }

        .detailed-stats {
            background: var(--bg-card);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-card);
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: var(--bg-glass);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 方向标签样式 */
        .direction-long {
            background: linear-gradient(135deg, var(--profit-color), rgba(0, 255, 136, 0.8));
            color: #000;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .direction-short {
            background: linear-gradient(135deg, var(--loss-color), rgba(255, 71, 87, 0.8));
            color: white;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 盈亏颜色 */
        .profit {
            color: var(--profit-color) !important;
        }

        .loss {
            color: var(--loss-color) !important;
        }

        /* 图表占位符 */
        .chart-placeholder {
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-card));
            border: 2px dashed var(--border-color);
            border-radius: 16px;
            padding: 60px 20px;
            text-align: center;
            color: var(--text-secondary);
            font-size: 18px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .chart-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                margin: 10px;
                border-radius: 16px;
            }

            .app-header {
                padding: 24px 20px;
            }

            .tab-content {
                padding: 20px;
            }

            .quick-add-form {
                max-width: 100%;
            }

            .button-group {
                grid-template-columns: 1fr;
            }

            .core-metrics {
                grid-template-columns: 1fr;
            }

            .stats-row {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .calendar-grid {
                font-size: 12px;
            }

            .calendar-day {
                min-height: 60px;
                padding: 8px;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a5acd);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1>📊 交易复盘工具</h1>
            <p>快速录入，智能分析，轻松复盘</p>
        </div>

        <div class="tab-container">
            <nav class="tab-nav">
                <button class="tab-button active" onclick="showTab('quick-add')">
                    ⚡ 快速录入
                </button>
                <button class="tab-button" onclick="showTab('journal')">
                    📅 交易日记
                </button>
                <button class="tab-button" onclick="showTab('analysis')">
                    📈 数据分析
                </button>
            </nav>

            <!-- 快速录入页面 -->
            <div id="quick-add" class="tab-content active">
                <div class="quick-add-form">
                    <div class="form-section">
                        <h3>💰 仓位大小</h3>
                        <div class="button-group">
                            <button class="quick-button" onclick="selectButton(this, '5000')">5千</button>
                            <button class="quick-button selected" onclick="selectButton(this, '10000')">1万</button>
                            <button class="quick-button" onclick="selectButton(this, '20000')">2万</button>
                            <button class="quick-button" onclick="selectButton(this, '50000')">5万</button>
                        </div>
                        <div class="custom-input">
                            <label>自定义：</label>
                            <input type="number" placeholder="输入金额" oninput="updateCalculations()">
                            <span>元</span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>📈 交易方向</h3>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="direction" value="long" checked onchange="updateCalculations()">
                                <span>📈 做多</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="direction" value="short" onchange="updateCalculations()">
                                <span>📉 做空</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>🎯 交易结果</h3>
                        <div class="button-group">
                            <button class="quick-button" onclick="selectResult(this, '+0.5')">+0.5%</button>
                            <button class="quick-button selected" onclick="selectResult(this, '+1.0')">+1.0%</button>
                            <button class="quick-button" onclick="selectResult(this, '+2.0')">+2.0%</button>
                            <button class="quick-button" onclick="selectResult(this, '-0.5')">-0.5%</button>
                            <button class="quick-button" onclick="selectResult(this, '-1.0')">-1.0%</button>
                            <button class="quick-button" onclick="selectResult(this, '-2.0')">-2.0%</button>
                        </div>
                        <div class="custom-input">
                            <label>自定义：</label>
                            <input type="number" step="0.1" placeholder="如：+1.5" oninput="updateCalculations()">
                            <span>%</span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>⏰ 交易时间</h3>
                        <div class="custom-input">
                            <label>开始时间：</label>
                            <input type="datetime-local" value="2024-01-15T14:30" onchange="updateCalculations()">
                        </div>
                        <div class="custom-input">
                            <label>结束时间：</label>
                            <input type="datetime-local" value="2024-01-15T15:45" onchange="updateCalculations()">
                        </div>
                        <div class="custom-input">
                            <label>持仓时间：</label>
                            <span id="duration-display" style="font-weight: bold; color: #667eea;">75 分钟</span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>💸 手续费率</h3>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">开仓手续费：</label>
                            <div class="button-group">
                                <button class="quick-button selected" onclick="selectEntryFee(this, '0.05')">0.05%</button>
                                <button class="quick-button" onclick="selectEntryFee(this, '0.1')">0.1%</button>
                                <button class="quick-button" onclick="selectEntryFee(this, '0.2')">0.2%</button>
                            </div>
                            <div class="custom-input">
                                <label>自定义：</label>
                                <input type="number" step="0.01" placeholder="0.05" oninput="updateCalculations()">
                                <span>%</span>
                            </div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">平仓手续费：</label>
                            <div class="button-group">
                                <button class="quick-button selected" onclick="selectExitFee(this, '0.05')">0.05%</button>
                                <button class="quick-button" onclick="selectExitFee(this, '0.1')">0.1%</button>
                                <button class="quick-button" onclick="selectExitFee(this, '0.2')">0.2%</button>
                            </div>
                            <div class="custom-input">
                                <label>自定义：</label>
                                <input type="number" step="0.01" placeholder="0.05" oninput="updateCalculations()">
                                <span>%</span>
                            </div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h3>💰 实时计算预览</h3>
                        <div class="preview-calculations">
                            <div class="calc-item">
                                <div class="label">毛收益</div>
                                <div class="value" id="gross-profit">+100元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">开仓费用</div>
                                <div class="value" id="entry-fees">-5元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">平仓费用</div>
                                <div class="value" id="exit-fees">-5元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">总手续费</div>
                                <div class="value" id="total-fees">-10元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">净收益</div>
                                <div class="value" id="net-profit">+90元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">净收益率</div>
                                <div class="value" id="return-rate">+0.90%</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section notes-section">
                        <h3>📝 交易备注</h3>
                        <textarea placeholder="记录交易原因、心得或其他重要信息..."></textarea>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary">💾 保存交易</button>
                        <button class="btn btn-secondary">💾 保存并继续</button>
                        <button class="btn btn-outline">📋 复制上一笔</button>
                        <button class="btn btn-outline">🏷️ 使用模板</button>
                    </div>
                </div>
            </div>

            <!-- 交易日记页面 -->
            <div id="journal" class="tab-content">
                <div class="calendar-container">
                    <div class="calendar-header">
                        <button class="calendar-nav" onclick="changeMonth(-1)">‹</button>
                        <h3>📅 2024年1月 交易日历</h3>
                        <button class="calendar-nav" onclick="changeMonth(1)">›</button>
                    </div>
                    <div class="calendar-grid">
                        <div class="calendar-weekday">日</div>
                        <div class="calendar-weekday">一</div>
                        <div class="calendar-weekday">二</div>
                        <div class="calendar-weekday">三</div>
                        <div class="calendar-weekday">四</div>
                        <div class="calendar-weekday">五</div>
                        <div class="calendar-weekday">六</div>
                        
                        <div class="calendar-day other-month">
                            <div class="day-number">31</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-01')">
                            <div class="day-number">1</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-02')">
                            <div class="day-number">2</div>
                        </div>
                        <div class="calendar-day profit" onclick="selectDay(this, '2024-01-03')">
                            <div class="day-number">3</div>
                            <div class="day-pnl">+¥120</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-04')">
                            <div class="day-number">4</div>
                        </div>
                        <div class="calendar-day loss" onclick="selectDay(this, '2024-01-05')">
                            <div class="day-number">5</div>
                            <div class="day-pnl">-¥85</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-06')">
                            <div class="day-number">6</div>
                        </div>
                        
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-07')">
                            <div class="day-number">7</div>
                        </div>
                        <div class="calendar-day profit" onclick="selectDay(this, '2024-01-08')">
                            <div class="day-number">8</div>
                            <div class="day-pnl">+¥340</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-09')">
                            <div class="day-number">9</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-10')">
                            <div class="day-number">10</div>
                        </div>
                        <div class="calendar-day profit" onclick="selectDay(this, '2024-01-11')">
                            <div class="day-number">11</div>
                            <div class="day-pnl">+¥75</div>
                        </div>
                        <div class="calendar-day loss" onclick="selectDay(this, '2024-01-12')">
                            <div class="day-number">12</div>
                            <div class="day-pnl">-¥160</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-13')">
                            <div class="day-number">13</div>
                        </div>
                        
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-14')">
                            <div class="day-number">14</div>
                        </div>
                        <div class="calendar-day profit" onclick="selectDay(this, '2024-01-15')">
                            <div class="day-number">15</div>
                            <div class="day-pnl">+¥190</div>
                        </div>
                        <div class="calendar-day selected today" onclick="selectDay(this, '2024-01-16')">
                            <div class="day-number">16</div>
                            <div class="day-pnl">+¥285</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-17')">
                            <div class="day-number">17</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-18')">
                            <div class="day-number">18</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-19')">
                            <div class="day-number">19</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-20')">
                            <div class="day-number">20</div>
                        </div>
                        
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-21')">
                            <div class="day-number">21</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-22')">
                            <div class="day-number">22</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-23')">
                            <div class="day-number">23</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-24')">
                            <div class="day-number">24</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-25')">
                            <div class="day-number">25</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-26')">
                            <div class="day-number">26</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-27')">
                            <div class="day-number">27</div>
                        </div>
                        
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-28')">
                            <div class="day-number">28</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-29')">
                            <div class="day-number">29</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-30')">
                            <div class="day-number">30</div>
                        </div>
                        <div class="calendar-day" onclick="selectDay(this, '2024-01-31')">
                            <div class="day-number">31</div>
                        </div>
                        <div class="calendar-day other-month">
                            <div class="day-number">1</div>
                        </div>
                        <div class="calendar-day other-month">
                            <div class="day-number">2</div>
                        </div>
                        <div class="calendar-day other-month">
                            <div class="day-number">3</div>
                        </div>
                    </div>
                </div>

                <div class="daily-summary">
                    <h3>📊 <span id="selected-date">2024年1月16日</span> 交易总结</h3>
                    <div class="summary-stats">
                        <div class="summary-stat">
                            <div class="label">交易次数</div>
                            <div class="value">3笔</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">胜率</div>
                            <div class="value profit">66.7%</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">总盈亏</div>
                            <div class="value profit">+¥285</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">手续费</div>
                            <div class="value">¥45</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">平均盈利</div>
                            <div class="value profit">+¥190</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">平均亏损</div>
                            <div class="value loss">-¥95</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">平均持仓</div>
                            <div class="value">65分钟</div>
                        </div>
                        <div class="summary-stat">
                            <div class="label">最大盈利</div>
                            <div class="value profit">+¥160</div>
                        </div>
                    </div>

                    <div class="daily-trades">
                        <h4>📝 当日交易明细</h4>
                        <div class="daily-trade-item profit-trade">
                            <div class="trade-details">
                                <span>14:30 - 15:45</span>
                                <span class="direction-long">做多</span>
                                <span>¥10,000</span>
                                <span>+1.2%</span>
                            </div>
                            <div class="trade-result">
                                <span class="profit">+¥110</span>
                            </div>
                        </div>
                        <div class="daily-trade-item loss-trade">
                            <div class="trade-details">
                                <span>10:15 - 12:15</span>
                                <span class="direction-short">做空</span>
                                <span>¥15,000</span>
                                <span>-0.8%</span>
                            </div>
                            <div class="trade-result">
                                <span class="loss">-¥135</span>
                            </div>
                        </div>
                        <div class="daily-trade-item profit-trade">
                            <div class="trade-details">
                                <span>16:20 - 17:05</span>
                                <span class="direction-long">做多</span>
                                <span>¥8,000</span>
                                <span>+2.1%</span>
                            </div>
                            <div class="trade-result">
                                <span class="profit">+¥160</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析页面 -->
            <div id="analysis" class="tab-content">
                <div class="analysis-header">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">📈 数据分析仪表板</h3>
                    <div class="time-range-selector">
                        <button class="range-button" onclick="selectRange(this, 'today')">今日</button>
                        <button class="range-button" onclick="selectRange(this, 'week')">本周</button>
                        <button class="range-button selected" onclick="selectRange(this, 'month')">本月</button>
                        <div class="custom-range">
                            <label>自定义：</label>
                            <input type="date" value="2024-01-01">
                            <span>至</span>
                            <input type="date" value="2024-01-31">
                            <button class="btn btn-primary" style="padding: 6px 12px; margin-left: 10px;">应用</button>
                        </div>
                    </div>
                    <div style="margin-top: 10px; color: #657786; font-size: 14px;">
                        分析时间段：2024年1月1日 - 2024年1月31日 (31天)
                    </div>
                </div>

                <div class="core-metrics">
                    <div class="metric-card">
                        <h4>总盈亏</h4>
                        <div class="metric-value profit">+¥2,847</div>
                        <div class="metric-subtitle">毛盈亏: +¥3,102 | 手续费: ¥255</div>
                    </div>
                    <div class="metric-card">
                        <h4>总体胜率</h4>
                        <div class="metric-value profit">65.2%</div>
                        <div class="metric-subtitle">23笔交易 | 15胜 8负</div>
                    </div>
                    <div class="metric-card">
                        <h4>多单胜率</h4>
                        <div class="metric-value profit">70.0%</div>
                        <div class="metric-subtitle">15笔多单 | 10胜 5负</div>
                    </div>
                    <div class="metric-card">
                        <h4>空单胜率</h4>
                        <div class="metric-value">58.3%</div>
                        <div class="metric-subtitle">8笔空单 | 5胜 3负</div>
                    </div>
                    <div class="metric-card">
                        <h4>平均盈利</h4>
                        <div class="metric-value profit">+¥186</div>
                        <div class="metric-subtitle">盈利交易平均值</div>
                    </div>
                    <div class="metric-card">
                        <h4>平均亏损</h4>
                        <div class="metric-value loss">-¥95</div>
                        <div class="metric-subtitle">亏损交易平均值</div>
                    </div>
                    <div class="metric-card">
                        <h4>最大盈利</h4>
                        <div class="metric-value profit">+¥450</div>
                        <div class="metric-subtitle">单笔最高收益</div>
                    </div>
                    <div class="metric-card">
                        <h4>最大亏损</h4>
                        <div class="metric-value loss">-¥320</div>
                        <div class="metric-subtitle">单笔最大损失</div>
                    </div>
                </div>

                <div class="chart-section">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">📊 资金曲线图</h3>
                    <div class="chart-placeholder" style="min-height: 300px;">
                        📈 累计盈亏变化趋势图
                        <br>
                        <small>显示每日累计盈亏变化情况，帮助识别交易表现趋势</small>
                        <br><br>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <strong>图表将显示：</strong><br>
                            • 每日累计盈亏线<br>
                            • 最大回撤区间<br>
                            • 交易频率分布<br>
                            • 盈亏波动范围
                        </div>
                    </div>
                </div>

                <div class="detailed-stats">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">📋 详细统计数据</h3>
                    
                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-label">总交易次数</span>
                            <span class="stat-value">23笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">盈利交易</span>
                            <span class="stat-value profit">15笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">亏损交易</span>
                            <span class="stat-value loss">8笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">盈亏比</span>
                            <span class="stat-value">1.96</span>
                        </div>
                    </div>

                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-label">手续费总额</span>
                            <span class="stat-value">¥255</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">手续费占比</span>
                            <span class="stat-value">0.05%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均持仓时间</span>
                            <span class="stat-value">89分钟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均仓位大小</span>
                            <span class="stat-value">¥12,348</span>
                        </div>
                    </div>

                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-label">连胜记录</span>
                            <span class="stat-value profit">5笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">连亏记录</span>
                            <span class="stat-value loss">3笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">交易日数</span>
                            <span class="stat-value">12天</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">日均交易</span>
                            <span class="stat-value">1.9笔</span>
                        </div>
                    </div>

                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-label">最长持仓</span>
                            <span class="stat-value">180分钟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最短持仓</span>
                            <span class="stat-value">30分钟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">盈利日数</span>
                            <span class="stat-value profit">8天</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">亏损日数</span>
                            <span class="stat-value loss">4天</span>
                        </div>
                    </div>

                    <div style="margin-top: 30px; text-align: center;">
                        <button class="btn btn-primary">📊 导出分析报告</button>
                        <button class="btn btn-outline">📈 导出图表</button>
                        <button class="btn btn-outline">💾 保存设置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const allTabs = document.querySelectorAll('.tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));
            
            // 移除所有按钮的活动状态
            const allButtons = document.querySelectorAll('.tab-button');
            allButtons.forEach(btn => btn.classList.remove('active'));
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 更新导航条位置
            const activeIndex = Array.from(allButtons).indexOf(event.target);
            document.documentElement.style.setProperty('--active-tab', activeIndex);
            
            // 添加页面切换动画
            setTimeout(() => {
                document.getElementById(tabName).style.transform = 'translateY(0)';
                document.getElementById(tabName).style.opacity = '1';
            }, 50);
        }

        // 快速按钮选择 - 添加动画效果
        function selectButton(button, value) {
            // 移除同组其他按钮的选中状态
            const group = button.parentElement;
            group.querySelectorAll('.quick-button').forEach(btn => {
                btn.classList.remove('selected');
                btn.style.transform = 'scale(1)';
            });
            
            // 选中当前按钮，添加动画
            button.classList.add('selected');
            button.style.transform = 'scale(1.05)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 200);
            
            updateCalculations();
        }

        // 交易结果选择 - 添加脉冲效果
        function selectResult(button, value) {
            selectButton(button, value);
            // 同时更新自定义输入框
            const customInput = button.parentElement.nextElementSibling.querySelector('input');
            customInput.value = value;
            
            // 添加计算预览的脉冲效果
            const previewSection = document.querySelector('.preview-section');
            previewSection.style.transform = 'scale(1.02)';
            setTimeout(() => {
                previewSection.style.transform = 'scale(1)';
            }, 300);
            
            updateCalculations();
        }

        // 手续费选择
        function selectEntryFee(button, value) {
            selectButton(button, value);
            updateCalculations();
        }

        function selectExitFee(button, value) {
            selectButton(button, value);
            updateCalculations();
        }

        // 更新计算结果 - 添加数值变化动画
        function updateCalculations() {
            // 获取当前值
            const position = getCurrentPosition();
            const result = getCurrentResult();
            const entryFee = getCurrentEntryFee();
            const exitFee = getCurrentExitFee();
            
            // 计算时间差
            updateDuration();
            
            // 计算
            const grossProfit = position * (result / 100);
            const entryFees = position * (entryFee / 100);
            const exitFees = position * (exitFee / 100);
            const totalFees = entryFees + exitFees;
            const netProfit = grossProfit - totalFees;
            const returnRate = (netProfit / position) * 100;
            
            // 带动画的数值更新
            animateValue('gross-profit', grossProfit, '元');
            animateValue('entry-fees', -entryFees, '元');
            animateValue('exit-fees', -exitFees, '元');
            animateValue('total-fees', -totalFees, '元');
            animateValue('net-profit', netProfit, '元');
            animateValue('return-rate', returnRate, '%');
            
            // 更新颜色
            updateColors(netProfit, returnRate);
        }

        // 数值动画函数
        function animateValue(elementId, targetValue, suffix) {
            const element = document.getElementById(elementId);
            const currentValue = parseFloat(element.textContent.replace(/[^-\d.]/g, '')) || 0;
            const difference = targetValue - currentValue;
            const steps = 20;
            const stepValue = difference / steps;
            let currentStep = 0;

            const interval = setInterval(() => {
                currentStep++;
                const newValue = currentValue + (stepValue * currentStep);
                const prefix = newValue >= 0 ? '+' : '';
                
                if (suffix === '%') {
                    element.textContent = prefix + newValue.toFixed(2) + suffix;
                } else {
                    element.textContent = prefix + newValue.toFixed(0) + suffix;
                }

                if (currentStep >= steps) {
                    clearInterval(interval);
                    // 最终值
                    const finalPrefix = targetValue >= 0 ? '+' : '';
                    if (suffix === '%') {
                        element.textContent = finalPrefix + targetValue.toFixed(2) + suffix;
                    } else {
                        element.textContent = finalPrefix + targetValue.toFixed(0) + suffix;
                    }
                }
            }, 20);
        }

        // 更新颜色
        function updateColors(netProfit, returnRate) {
            const netElement = document.getElementById('net-profit');
            const rateElement = document.getElementById('return-rate');
            
            const color = netProfit >= 0 ? 'var(--profit-color)' : 'var(--loss-color)';
            netElement.style.color = color;
            rateElement.style.color = color;
            
            // 添加闪烁效果
            [netElement, rateElement].forEach(el => {
                el.style.textShadow = `0 0 10px ${color}`;
                setTimeout(() => {
                    el.style.textShadow = 'none';
                }, 500);
            });
        }

        // 更新持仓时间
        function updateDuration() {
            const entryTimeInput = document.querySelector('.form-section:nth-child(4) input[type="datetime-local"]:first-of-type');
            const exitTimeInput = document.querySelector('.form-section:nth-child(4) input[type="datetime-local"]:last-of-type');
            
            if (entryTimeInput.value && exitTimeInput.value) {
                const entryTime = new Date(entryTimeInput.value);
                const exitTime = new Date(exitTimeInput.value);
                const diffMinutes = Math.round((exitTime - entryTime) / (1000 * 60));
                
                const durationElement = document.getElementById('duration-display');
                if (diffMinutes >= 0) {
                    durationElement.textContent = diffMinutes + ' 分钟';
                    durationElement.style.color = 'var(--profit-color)';
                } else {
                    durationElement.textContent = '时间错误';
                    durationElement.style.color = 'var(--loss-color)';
                }
            }
        }

        // 获取当前仓位
        function getCurrentPosition() {
            const selectedButton = document.querySelector('.form-section:first-child .quick-button.selected');
            if (selectedButton) {
                const text = selectedButton.textContent;
                if (text.includes('千')) return parseInt(text) * 1000;
                if (text.includes('万')) return parseInt(text) * 10000;
            }
            
            const customInput = document.querySelector('.form-section:first-child input[type="number"]');
            return parseFloat(customInput.value) || 10000;
        }

        // 获取当前交易结果
        function getCurrentResult() {
            const selectedButton = document.querySelector('.form-section:nth-child(3) .quick-button.selected');
            if (selectedButton) {
                return parseFloat(selectedButton.textContent.replace('%', ''));
            }
            
            const customInput = document.querySelector('.form-section:nth-child(3) input[type="number"]');
            return parseFloat(customInput.value) || 1.0;
        }

        // 获取当前开仓手续费
        function getCurrentEntryFee() {
            const selectedButton = document.querySelector('.form-section:nth-child(5) .button-group:first-of-type .quick-button.selected');
            if (selectedButton) {
                return parseFloat(selectedButton.textContent.replace('%', ''));
            }
            
            const customInput = document.querySelector('.form-section:nth-child(5) .custom-input:first-of-type input[type="number"]');
            return parseFloat(customInput.value) || 0.05;
        }

        // 获取当前平仓手续费
        function getCurrentExitFee() {
            const selectedButton = document.querySelector('.form-section:nth-child(5) .button-group:last-of-type .quick-button.selected');
            if (selectedButton) {
                return parseFloat(selectedButton.textContent.replace('%', ''));
            }
            
            const customInput = document.querySelector('.form-section:nth-child(5) .custom-input:last-of-type input[type="number"]');
            return parseFloat(customInput.value) || 0.05;
        }

        // 初始化计算
        updateCalculations();

        // 交易日记相关函数 - 添加选择动画
        function selectDay(dayElement, date) {
            // 移除其他天的选中状态
            document.querySelectorAll('.calendar-day').forEach(day => {
                day.classList.remove('selected');
                day.style.transform = 'scale(1)';
            });
            
            // 选中当前天，添加动画
            dayElement.classList.add('selected');
            dayElement.style.transform = 'scale(1.1)';
            setTimeout(() => {
                dayElement.style.transform = 'scale(1)';
            }, 300);
            
            // 更新选中日期显示
            const dateObj = new Date(date);
            const year = dateObj.getFullYear();
            const month = dateObj.getMonth() + 1;
            const day = dateObj.getDate();
            document.getElementById('selected-date').textContent = 
                `${year}年${month}月${day}日`;
            
            // 添加日期更新动画
            const summarySection = document.querySelector('.daily-summary');
            summarySection.style.transform = 'translateX(-20px)';
            summarySection.style.opacity = '0.7';
            setTimeout(() => {
                summarySection.style.transform = 'translateX(0)';
                summarySection.style.opacity = '1';
            }, 200);
            
            // 这里可以加载该日期的实际交易数据
            loadDayTrades(date);
        }

        function changeMonth(direction) {
            // 月份切换动画
            const calendarGrid = document.querySelector('.calendar-grid');
            calendarGrid.style.transform = `translateX(${direction > 0 ? '20px' : '-20px'})`;
            calendarGrid.style.opacity = '0.5';
            
            setTimeout(() => {
                calendarGrid.style.transform = 'translateX(0)';
                calendarGrid.style.opacity = '1';
                // 这里应该实现月份切换逻辑
                console.log('切换月份:', direction);
            }, 300);
        }

        function loadDayTrades(date) {
            // 模拟加载该日期的交易数据
            console.log('加载日期交易数据:', date);
        }

        // 数据分析相关函数 - 添加切换动画
        function selectRange(button, range) {
            // 移除其他按钮的选中状态
            document.querySelectorAll('.range-button').forEach(btn => {
                btn.classList.remove('selected');
                btn.style.transform = 'scale(1)';
            });
            
            // 选中当前按钮，添加动画
            button.classList.add('selected');
            button.style.transform = 'scale(1.05)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 200);
            
            // 添加数据更新动画
            const metricsCards = document.querySelectorAll('.metric-card');
            metricsCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        card.style.transform = 'translateY(0)';
                    }, 200);
                }, index * 100);
            });
            
            // 根据选择更新数据
            updateAnalysisData(range);
        }

        function updateAnalysisData(range) {
            // 根据时间范围更新分析数据
            console.log('更新分析数据:', range);
            
            // 更新时间段显示
            let periodText = '';
            switch(range) {
                case 'today':
                    periodText = '2024年1月16日 (1天)';
                    break;
                case 'week':
                    periodText = '2024年1月10日 - 2024年1月16日 (7天)';
                    break;
                case 'month':
                    periodText = '2024年1月1日 - 2024年1月31日 (31天)';
                    break;
            }
            
            if (periodText) {
                const periodElement = document.querySelector('.analysis-header div:last-child');
                periodElement.style.opacity = '0.5';
                setTimeout(() => {
                    periodElement.textContent = '分析时间段：' + periodText;
                    periodElement.style.opacity = '1';
                }, 200);
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面加载动画
            const appContainer = document.querySelector('.app-container');
            appContainer.style.transform = 'translateY(50px)';
            appContainer.style.opacity = '0';
            
            setTimeout(() => {
                appContainer.style.transition = 'all 0.8s ease';
                appContainer.style.transform = 'translateY(0)';
                appContainer.style.opacity = '1';
            }, 100);
            
            // 为输入框添加焦点效果
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
            
            // 为按钮添加点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html> 