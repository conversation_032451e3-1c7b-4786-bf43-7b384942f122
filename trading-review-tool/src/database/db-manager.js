const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

class DatabaseManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
    }

    /**
     * 初始化数据库连接
     * @param {string} dbPath - 数据库文件路径
     */
    async initialize(dbPath) {
        try {
            // 确保数据目录存在
            const dataDir = path.dirname(dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // 创建数据库连接
            this.db = new Database(dbPath);

            // 启用外键约束
            this.db.pragma('foreign_keys = ON');

            // 设置WAL模式以提高并发性能
            this.db.pragma('journal_mode = WAL');

            // 执行数据库结构初始化
            await this.initializeSchema();

            this.isInitialized = true;
            console.log('数据库初始化成功:', dbPath);

        } catch (error) {
            console.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 执行数据库结构初始化
     */
    async initializeSchema() {
        try {
            const schemaPath = path.join(__dirname, 'schema.sql');
            const schemaSql = fs.readFileSync(schemaPath, 'utf8');

            // 分割SQL语句并执行
            const statements = schemaSql
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0);

            for (const statement of statements) {
                this.db.exec(statement);
            }

            console.log('数据库结构初始化完成');

        } catch (error) {
            console.error('数据库结构初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库连接
     */
    getConnection() {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }
        return this.db;
    }

    /**
     * 执行事务
     * @param {Function} callback - 事务回调函数
     */
    transaction(callback) {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        const transaction = this.db.transaction(callback);
        return transaction;
    }

    /**
     * 执行查询
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数
     */
    query(sql, params = []) {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            const stmt = this.db.prepare(sql);
            return stmt.all(params);
        } catch (error) {
            console.error('查询执行失败:', error);
            throw error;
        }
    }

    /**
     * 执行单行查询
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数
     */
    queryOne(sql, params = []) {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            const stmt = this.db.prepare(sql);
            return stmt.get(params);
        } catch (error) {
            console.error('单行查询执行失败:', error);
            throw error;
        }
    }

    /**
     * 执行插入/更新/删除操作
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数
     */
    execute(sql, params = []) {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            const stmt = this.db.prepare(sql);
            return stmt.run(params);
        } catch (error) {
            console.error('执行操作失败:', error);
            throw error;
        }
    }

    /**
     * 批量执行操作
     * @param {string} sql - SQL语句
     * @param {Array} dataArray - 数据数组
     */
    executeBatch(sql, dataArray) {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            const stmt = this.db.prepare(sql);
            const transaction = this.db.transaction((data) => {
                for (const item of data) {
                    stmt.run(item);
                }
            });

            return transaction(dataArray);
        } catch (error) {
            console.error('批量执行失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库统计信息
     */
    getStats() {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            const stats = {
                trades: this.queryOne('SELECT COUNT(*) as count FROM trades').count,
                templates: this.queryOne('SELECT COUNT(*) as count FROM trade_templates').count,
                preferences: this.queryOne('SELECT COUNT(*) as count FROM user_preferences').count,
                dbSize: this.getDbSize()
            };

            return stats;
        } catch (error) {
            console.error('获取统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库文件大小
     */
    getDbSize() {
        try {
            const dbPath = this.db.name;
            const stats = fs.statSync(dbPath);
            return stats.size;
        } catch (error) {
            console.error('获取数据库大小失败:', error);
            return 0;
        }
    }

    /**
     * 备份数据库
     * @param {string} backupPath - 备份文件路径
     */
    backup(backupPath) {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            const backupDb = new Database(backupPath);
            this.db.backup(backupDb);
            backupDb.close();
            console.log('数据库备份成功:', backupPath);
        } catch (error) {
            console.error('数据库备份失败:', error);
            throw error;
        }
    }

    /**
     * 优化数据库
     */
    optimize() {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            this.db.pragma('optimize');
            this.db.exec('VACUUM');
            console.log('数据库优化完成');
        } catch (error) {
            console.error('数据库优化失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            try {
                this.db.close();
                this.db = null;
                this.isInitialized = false;
                console.log('数据库连接已关闭');
            } catch (error) {
                console.error('关闭数据库连接失败:', error);
            }
        }
    }

    /**
     * 检查数据库连接状态
     */
    isConnected() {
        return this.isInitialized && this.db && this.db.open;
    }

    /**
     * 获取交易品种历史
     */
    getInstrumentHistory() {
        if (!this.isInitialized || !this.db) {
            throw new Error('数据库未初始化');
        }

        try {
            // 从交易记录中获取所有不同的交易品种，按使用频率排序
            const instruments = this.query(`
                SELECT instrument, COUNT(*) as usage_count
                FROM trades
                WHERE instrument IS NOT NULL AND instrument != ''
                GROUP BY instrument
                ORDER BY usage_count DESC, MAX(created_at) DESC
                LIMIT 20
            `);

            return instruments.map(item => item.instrument);
        } catch (error) {
            console.error('获取交易品种历史失败:', error);
            // 如果查询失败，返回空数组
            return [];
        }
    }

    /**
     * 保存交易品种历史（这里实际上不需要单独保存，因为从交易记录中动态获取）
     * 保留此方法以保持API一致性
     */
    saveInstrumentHistory(instruments) {
        // 由于我们从交易记录中动态获取交易品种历史，
        // 这个方法实际上不需要做任何事情
        // 但保留它以保持前端API的一致性
        console.log('交易品种历史已更新（从交易记录中动态获取）');
        return true;
    }
}

// 导出单例实例
const dbManager = new DatabaseManager();
module.exports = dbManager;