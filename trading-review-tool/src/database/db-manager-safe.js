const path = require('path');
const fs = require('fs');

class SafeDatabaseManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.mode = 'unknown'; // 'sqlite' | 'memory'
        this.initError = null; // 记录初始化错误
        this.memoryData = {
            trades: [],
            preferences: new Map(),
            templates: [],
            nextId: 1
        };
    }

    /**
     * 初始化数据库连接（带fallback机制）
     * @param {string} dbPath - 数据库文件路径
     */
    async initialize(dbPath) {
        try {
            // 首先尝试SQLite模式
            await this.initializeSQLite(dbPath);
            this.mode = 'sqlite';
            this.initError = null;
            console.log('✅ SQLite数据库初始化成功:', dbPath);

        } catch (sqliteError) {
            console.warn('⚠️ SQLite初始化失败，切换到内存模式:', sqliteError.message);
            this.initError = sqliteError.message;

            // SQLite失败，切换到内存模式
            await this.initializeMemory();
            this.mode = 'memory';
            console.log('✅ 内存数据库初始化成功');
        }

        this.isInitialized = true;
    }

    /**
     * 初始化SQLite数据库
     */
    async initializeSQLite(dbPath) {
        try {
            const Database = require('better-sqlite3');

            // 确保数据目录存在
            const dataDir = path.dirname(dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // 创建数据库连接
            this.db = new Database(dbPath);

            // 启用外键约束
            this.db.pragma('foreign_keys = ON');

            // 设置WAL模式以提高并发性能
            this.db.pragma('journal_mode = WAL');

            // 执行数据库结构初始化
            await this.initializeSchema();

            console.log('SQLite数据库连接创建成功');

        } catch (error) {
            console.error('SQLite初始化详细错误:', error);
            throw error;
        }
    }

    /**
     * 初始化内存数据库
     */
    async initializeMemory() {
        // 初始化内存数据结构
        this.memoryData = {
            trades: [],
            preferences: new Map([
                ['default_position_size', '10000'],
                ['default_entry_fee', '0.05'],
                ['default_exit_fee', '0.05'],
                ['recent_position_sizes', '[]']
            ]),
            templates: [
                { id: 1, name: '默认模板', position_size: 10000, entry_fee_pct: 0.05, exit_fee_pct: 0.05, notes: '标准交易模板', is_default: 1 }
            ],
            nextId: 1
        };

        console.log('内存数据库结构初始化完成');
    }

    /**
     * 执行数据库结构初始化
     */
    async initializeSchema() {
        try {
            const schemaPath = path.join(__dirname, 'schema.sql');

            if (!fs.existsSync(schemaPath)) {
                throw new Error(`Schema文件不存在: ${schemaPath}`);
            }

            const schemaSql = fs.readFileSync(schemaPath, 'utf8');

            // 直接执行整个SQL文件，让SQLite处理语句分割
            try {
                this.db.exec(schemaSql);
            } catch (execError) {
                console.error('执行Schema SQL失败:', execError);
                throw execError;
            }

            console.log('数据库结构初始化完成');

        } catch (error) {
            console.error('数据库结构初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库连接
     */
    getConnection() {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }
        return this.db;
    }

    /**
     * 执行查询（兼容SQLite和内存模式）
     */
    query(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        if (this.mode === 'sqlite') {
            try {
                const stmt = this.db.prepare(sql);
                return stmt.all(params);
            } catch (error) {
                console.error('SQLite查询执行失败:', error);
                throw error;
            }
        } else {
            // 内存模式查询
            return this.executeMemoryQuery(sql, params);
        }
    }

    /**
     * 执行单行查询
     */
    queryOne(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        if (this.mode === 'sqlite') {
            try {
                const stmt = this.db.prepare(sql);
                return stmt.get(params);
            } catch (error) {
                console.error('SQLite单行查询执行失败:', error);
                throw error;
            }
        } else {
            // 内存模式查询
            const results = this.executeMemoryQuery(sql, params);
            return results.length > 0 ? results[0] : null;
        }
    }

    /**
     * 执行插入/更新/删除操作
     */
    execute(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        if (this.mode === 'sqlite') {
            try {
                const stmt = this.db.prepare(sql);
                return stmt.run(params);
            } catch (error) {
                console.error('SQLite执行操作失败:', error);
                throw error;
            }
        } else {
            // 内存模式执行
            return this.executeMemoryCommand(sql, params);
        }
    }

    /**
     * 内存模式查询执行器
     */
    executeMemoryQuery(sql, params) {
        const sqlLower = sql.toLowerCase().trim();

        if (sqlLower.includes('select') && sqlLower.includes('trades')) {
            if (sqlLower.includes('count(*)')) {
                return [{ count: this.memoryData.trades.length }];
            }
            // 返回所有交易记录的副本
            return this.memoryData.trades.map(trade => ({ ...trade }));
        }

        if (sqlLower.includes('select') && sqlLower.includes('user_preferences')) {
            const results = [];
            for (const [key, value] of this.memoryData.preferences) {
                results.push({ key, value, category: 'trading' });
            }
            return results;
        }

        return [];
    }

    /**
     * 内存模式命令执行器
     */
    executeMemoryCommand(sql, params) {
        const sqlLower = sql.toLowerCase().trim();

        if (sqlLower.startsWith('insert') && sqlLower.includes('trades')) {
            // 添加交易记录
            const trade = {
                id: this.memoryData.nextId++,
                position_size: parseFloat(params[0]) || 0,
                direction: params[1] || '',
                result_pct: parseFloat(params[2]) || 0,
                entry_time: params[3] || '',
                exit_time: params[4] || '',
                entry_fee_pct: parseFloat(params[5]) || 0.05,
                exit_fee_pct: parseFloat(params[6]) || 0.05,
                instrument: params[7] || '',
                notes: params[8] || '',
                created_at: new Date().toISOString()
            };

            // 计算字段 - 确保数值计算正确
            trade.gross_pnl = trade.position_size * trade.result_pct / 100;
            trade.entry_fees = trade.position_size * trade.entry_fee_pct / 100;
            trade.exit_fees = trade.position_size * trade.exit_fee_pct / 100;
            trade.total_fees = trade.entry_fees + trade.exit_fees;
            trade.net_pnl = trade.gross_pnl - trade.total_fees;

            // 计算持仓时间
            if (trade.entry_time && trade.exit_time) {
                const entryTime = new Date(trade.entry_time);
                const exitTime = new Date(trade.exit_time);
                trade.duration_minutes = Math.max(0, (exitTime - entryTime) / (1000 * 60));
            } else {
                trade.duration_minutes = 0;
            }

            trade.return_rate = trade.position_size > 0 ? (trade.net_pnl / trade.position_size * 100) : 0;

            this.memoryData.trades.push(trade);
            console.log('内存模式添加交易记录:', trade);

            return { lastInsertRowid: trade.id, changes: 1 };
        }

        if (sqlLower.startsWith('delete') && sqlLower.includes('trades')) {
            const id = parseInt(params[0]);
            const index = this.memoryData.trades.findIndex(t => t.id === id);
            if (index > -1) {
                const deletedTrade = this.memoryData.trades.splice(index, 1)[0];
                console.log('内存模式删除交易记录:', deletedTrade);
                return { changes: 1 };
            }
            return { changes: 0 };
        }

        return { changes: 0 };
    }

    /**
     * 获取数据库统计信息
     */
    getStats() {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        if (this.mode === 'sqlite') {
            try {
                const stats = {
                    trades: this.queryOne('SELECT COUNT(*) as count FROM trades').count,
                    templates: this.queryOne('SELECT COUNT(*) as count FROM trade_templates').count,
                    preferences: this.queryOne('SELECT COUNT(*) as count FROM user_preferences').count,
                    dbSize: this.getDbSize()
                };
                return stats;
            } catch (error) {
                console.error('获取SQLite统计信息失败:', error);
                throw error;
            }
        } else {
            return {
                trades: this.memoryData.trades.length,
                templates: this.memoryData.templates.length,
                preferences: this.memoryData.preferences.size,
                dbSize: 0
            };
        }
    }

    /**
     * 获取数据库文件大小
     */
    getDbSize() {
        if (this.mode === 'memory') return 0;

        try {
            const dbPath = this.db.name;
            const stats = fs.statSync(dbPath);
            return stats.size;
        } catch (error) {
            console.error('获取数据库大小失败:', error);
            return 0;
        }
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db && this.mode === 'sqlite') {
            try {
                this.db.close();
                this.db = null;
                console.log('SQLite数据库连接已关闭');
            } catch (error) {
                console.error('关闭SQLite数据库连接失败:', error);
            }
        }

        if (this.mode === 'memory') {
            console.log('内存数据库已清理');
        }

        this.isInitialized = false;
        this.mode = 'unknown';
    }

    /**
     * 检查数据库连接状态
     */
    isConnected() {
        return this.isInitialized;
    }

    /**
     * 获取当前数据库模式
     */
    getMode() {
        return this.mode;
    }

    /**
     * 获取初始化错误信息
     */
    getInitError() {
        return this.initError;
    }

    /**
     * 获取交易品种历史
     */
    getInstrumentHistory() {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        try {
            if (this.mode === 'sqlite') {
                // 从交易记录中获取所有不同的交易品种，按使用频率排序
                const instruments = this.query(`
                    SELECT instrument, COUNT(*) as usage_count
                    FROM trades
                    WHERE instrument IS NOT NULL AND instrument != ''
                    GROUP BY instrument
                    ORDER BY usage_count DESC, MAX(created_at) DESC
                    LIMIT 20
                `);

                return instruments.map(item => item.instrument);
            } else {
                // 内存模式：从内存数据中获取
                const instrumentMap = new Map();
                this.memoryData.trades.forEach(trade => {
                    if (trade.instrument && trade.instrument.trim()) {
                        const instrument = trade.instrument.trim();
                        instrumentMap.set(instrument, (instrumentMap.get(instrument) || 0) + 1);
                    }
                });

                // 按使用频率排序
                return Array.from(instrumentMap.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 20)
                    .map(item => item[0]);
            }
        } catch (error) {
            console.error('获取交易品种历史失败:', error);
            // 如果查询失败，返回空数组
            return [];
        }
    }

    /**
     * 保存交易品种历史（这里实际上不需要单独保存，因为从交易记录中动态获取）
     * 保留此方法以保持API一致性
     */
    saveInstrumentHistory(instruments) {
        // 由于我们从交易记录中动态获取交易品种历史，
        // 这个方法实际上不需要做任何事情
        // 但保留它以保持前端API的一致性
        console.log('交易品种历史已更新（从交易记录中动态获取）');
        return true;
    }
}

// 导出单例实例
const safeDbManager = new SafeDatabaseManager();
module.exports = safeDbManager;