<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易复盘工具 - 第三步：快速录入功能</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            --bg-primary: #0f0f23;
            --bg-secondary: #1a1a2e;
            --bg-card: rgba(255, 255, 255, 0.05);
            --bg-glass: rgba(255, 255, 255, 0.08);

            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;

            --border-color: rgba(255, 255, 255, 0.1);
            --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-hover: 0 12px 48px rgba(0, 0, 0, 0.4);

            --profit-color: #00ff88;
            --loss-color: #ff4757;
            --neutral-color: #74b9ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .app-container {
            max-width: 1400px;
            margin: 20px auto;
            background: var(--bg-glass);
            border-radius: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .app-header {
            background: var(--primary-gradient);
            padding: 32px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        .app-header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .app-header p {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 40px;
        }

        .section {
            background: var(--bg-card);
            border-radius: 20px;
            padding: 32px;
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            margin-bottom: 24px;
            box-shadow: var(--shadow-card);
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .section h2 {
            color: var(--text-primary);
            margin-bottom: 24px;
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 快速录入表单样式 */
        .quick-add-form {
            max-width: 700px;
            margin: 0 auto;
        }

        .form-section {
            margin-bottom: 32px;
            padding: 24px;
            background: var(--bg-card);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .form-section:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .form-section h3 {
            margin-bottom: 20px;
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .quick-button {
            padding: 12px 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-family: inherit;
        }

        .quick-button:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .quick-button.selected {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        /* 自定义输入框样式 */
        .custom-input {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 16px;
        }

        .custom-input label {
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 80px;
        }

        .custom-input input {
            padding: 12px 16px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 14px;
            color: var(--text-primary);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .custom-input input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        /* 单选按钮组样式 */
        .radio-group {
            display: flex;
            gap: 24px;
            margin-bottom: 20px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            font-size: 16px;
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        .radio-option:hover {
            color: var(--text-primary);
        }

        .radio-option input[type="radio"] {
            width: 20px;
            height: 20px;
            accent-color: #667eea;
        }

        /* 实时计算预览区域 */
        .preview-section {
            background: var(--primary-gradient);
            padding: 32px;
            border-radius: 16px;
            margin: 32px 0;
            position: relative;
            overflow: hidden;
        }

        .preview-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 10% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .preview-section h3 {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            position: relative;
            z-index: 1;
        }

        .preview-calculations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .calc-item {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .calc-item:hover {
            transform: translateY(-3px);
            background: rgba(255, 255, 255, 0.15);
        }

        .calc-item .label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .calc-item .value {
            font-size: 20px;
            font-weight: 700;
        }

        /* 备注区域样式 */
        .notes-section textarea {
            width: 100%;
            padding: 16px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 14px;
            color: var(--text-primary);
            font-family: inherit;
            resize: vertical;
            min-height: 100px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .notes-section textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .notes-section textarea::placeholder {
            color: var(--text-muted);
        }

        /* 操作按钮组样式 */
        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-family: inherit;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: var(--bg-card);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: var(--bg-glass);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-outline:hover {
            background: var(--bg-card);
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 8px 24px rgba(79, 172, 254, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(79, 172, 254, 0.4);
        }

        /* 小型删除按钮样式 */
        .btn-delete {
            background: var(--danger-gradient);
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            min-width: 60px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .btn-delete:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(255, 107, 107, 0.3);
        }

        .trades-list {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }

        .trade-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: background-color 0.2s ease;
        }

        .trade-item:hover {
            background-color: #f8f9fa;
        }

        .trade-item:last-child {
            border-bottom: none;
        }

        .trade-info {
            flex: 1;
            display: grid;
            grid-template-columns: 60px 100px 60px 80px 90px 80px 70px 80px;
            gap: 12px;
            align-items: center;
        }

        .trade-field {
            text-align: center;
        }

        .trade-field.id-field {
            text-align: left;
        }

        .trade-field .label {
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 2px;
            line-height: 1.2;
        }

        .trade-field .value {
            font-weight: 600;
            color: #2c3e50;
            font-size: 13px;
            line-height: 1.3;
        }

        .trade-actions {
            flex-shrink: 0;
        }

        .profit {
            color: #28a745;
        }

        .loss {
            color: #dc3545;
        }

        .direction-long {
            color: #28a745;
            font-weight: bold;
        }

        .direction-short {
            color: #dc3545;
            font-weight: bold;
        }

        .status-bar {
            background: #2c3e50;
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .statistics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .datetime-input {
            position: relative;
        }

        .quick-time-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #6c757d;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .quick-time-btn:hover {
            background: #495057;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .trade-info {
                grid-template-columns: 1fr 1fr;
                gap: 8px;
            }

            .trade-field .label {
                font-size: 10px;
            }

            .trade-field .value {
                font-size: 12px;
            }

            .btn-delete {
                font-size: 11px;
                padding: 4px 6px;
                min-width: 50px;
                height: 28px;
            }
        }

        /* 消息提示样式 */
        #message-area {
            margin-bottom: 20px;
        }

        #message-area .success {
            background: linear-gradient(135deg, #00ff88, #00d4aa);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            font-weight: 500;
            box-shadow: 0 4px 16px rgba(0, 255, 136, 0.3);
            animation: slideIn 0.3s ease;
        }

        #message-area .error {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            font-weight: 500;
            box-shadow: 0 4px 16px rgba(255, 71, 87, 0.3);
            animation: slideIn 0.3s ease;
        }

        #message-area .info {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            font-weight: 500;
            box-shadow: 0 4px 16px rgba(116, 185, 255, 0.3);
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .app-container {
                margin: 10px;
            }

            .preview-calculations {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .app-header {
                padding: 20px;
            }

            .app-header h1 {
                font-size: 24px;
            }

            .section {
                padding: 20px;
            }

            .button-group {
                grid-template-columns: repeat(2, 1fr);
            }

            .preview-calculations {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: stretch;
            }

            .custom-input {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .custom-input label {
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .app-container {
                margin: 5px;
                border-radius: 16px;
            }

            .main-content {
                padding: 15px;
            }

            .section {
                padding: 16px;
            }

            .button-group {
                grid-template-columns: 1fr;
            }

            .radio-group {
                flex-direction: column;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1>📊 交易复盘工具</h1>
            <p>第三步：快速录入功能 - 智能交易记录系统</p>
        </div>

        <div class="main-content">
            <!-- 快速录入区域 -->
            <div class="section">
                <h2>⚡ 快速录入</h2>

                <div id="message-area"></div>

                <div class="quick-add-form">
                    <div class="form-section">
                        <h3>💰 仓位大小</h3>
                        <div class="button-group">
                            <button class="quick-button" onclick="selectPositionSize(this, '5000')">5千</button>
                            <button class="quick-button selected" onclick="selectPositionSize(this, '10000')">1万</button>
                            <button class="quick-button" onclick="selectPositionSize(this, '20000')">2万</button>
                            <button class="quick-button" onclick="selectPositionSize(this, '50000')">5万</button>
                        </div>
                        <div class="custom-input">
                            <label>自定义：</label>
                            <input type="number" id="position-size" placeholder="输入金额" oninput="updateCalculations()">
                            <span>元</span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>📈 交易方向</h3>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="direction" value="Long" checked onchange="updateCalculations()">
                                <span>📈 做多</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="direction" value="Short" onchange="updateCalculations()">
                                <span>📉 做空</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>🎯 交易结果</h3>
                        <div class="button-group">
                            <button class="quick-button" onclick="selectResult(this, '+0.5')">+0.5%</button>
                            <button class="quick-button selected" onclick="selectResult(this, '+1.0')">+1.0%</button>
                            <button class="quick-button" onclick="selectResult(this, '+2.0')">+2.0%</button>
                            <button class="quick-button" onclick="selectResult(this, '-0.5')">-0.5%</button>
                            <button class="quick-button" onclick="selectResult(this, '-1.0')">-1.0%</button>
                            <button class="quick-button" onclick="selectResult(this, '-2.0')">-2.0%</button>
                        </div>
                        <div class="custom-input">
                            <label>自定义：</label>
                            <input type="number" id="result-pct" step="0.1" placeholder="如：+1.5" oninput="updateCalculations()">
                            <span>%</span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>⏰ 交易时间</h3>
                        <div class="custom-input">
                            <label>开始时间：</label>
                            <input type="datetime-local" id="entry-time" onchange="updateCalculations()">
                            <button type="button" class="quick-button" onclick="setCurrentTime('entry-time')">现在</button>
                        </div>
                        <div class="custom-input">
                            <label>结束时间：</label>
                            <input type="datetime-local" id="exit-time" onchange="updateCalculations()">
                            <button type="button" class="quick-button" onclick="setCurrentTime('exit-time')">现在</button>
                        </div>
                        <div class="custom-input">
                            <label>持仓时间：</label>
                            <span id="duration-display" style="font-weight: bold; color: #667eea;">-- 分钟</span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>💸 手续费率</h3>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">开仓手续费：</label>
                            <div class="button-group">
                                <button class="quick-button selected" onclick="selectEntryFee(this, '0.05')">0.05%</button>
                                <button class="quick-button" onclick="selectEntryFee(this, '0.1')">0.1%</button>
                                <button class="quick-button" onclick="selectEntryFee(this, '0.2')">0.2%</button>
                            </div>
                            <div class="custom-input">
                                <label>自定义：</label>
                                <input type="number" id="entry-fee" step="0.01" placeholder="0.05" oninput="updateCalculations()">
                                <span>%</span>
                            </div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">平仓手续费：</label>
                            <div class="button-group">
                                <button class="quick-button selected" onclick="selectExitFee(this, '0.05')">0.05%</button>
                                <button class="quick-button" onclick="selectExitFee(this, '0.1')">0.1%</button>
                                <button class="quick-button" onclick="selectExitFee(this, '0.2')">0.2%</button>
                            </div>
                            <div class="custom-input">
                                <label>自定义：</label>
                                <input type="number" id="exit-fee" step="0.01" placeholder="0.05" oninput="updateCalculations()">
                                <span>%</span>
                            </div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h3>💰 实时计算预览</h3>
                        <div class="preview-calculations">
                            <div class="calc-item">
                                <div class="label">毛收益</div>
                                <div class="value" id="gross-profit">+0元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">开仓费用</div>
                                <div class="value" id="entry-fees">-0元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">平仓费用</div>
                                <div class="value" id="exit-fees">-0元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">总手续费</div>
                                <div class="value" id="total-fees">-0元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">净收益</div>
                                <div class="value" id="net-profit">+0元</div>
                            </div>
                            <div class="calc-item">
                                <div class="label">净收益率</div>
                                <div class="value" id="return-rate">+0.00%</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section notes-section">
                        <h3>📝 交易备注</h3>
                        <textarea id="notes" placeholder="记录交易原因、心得或其他重要信息..."></textarea>
                        <div class="custom-input" style="margin-top: 16px;">
                            <label>交易品种：</label>
                            <input type="text" id="instrument" placeholder="例如：BTC/USDT">
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveTrade()">💾 保存交易</button>
                        <button class="btn btn-secondary" onclick="saveAndContinue()">💾 保存并继续</button>
                        <button class="btn btn-outline" onclick="copyLastTrade()">📋 复制上一笔</button>
                        <button class="btn btn-outline" onclick="useTemplate()">🏷️ 使用模板</button>
                    </div>
                </div>
            </div>

            <!-- 交易记录列表 -->
            <div class="section">
                <h2>📊 交易记录</h2>

                <!-- 统计信息 -->
                <div class="statistics" id="statistics">
                    <div class="stat-card">
                        <div class="stat-value" id="total-trades">0</div>
                        <div class="stat-label">总交易数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="win-rate">0%</div>
                        <div class="stat-label">胜率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-pnl">¥0</div>
                        <div class="stat-label">总盈亏</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="avg-pnl">¥0</div>
                        <div class="stat-label">平均盈亏</div>
                    </div>
                </div>

                <div class="loading" id="loading">加载中...</div>

                <div class="trades-list" id="trades-list">
                    <!-- 交易记录将在这里动态加载 -->
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <span>🗄️ 数据库状态：</span>
                <span id="db-status">连接中...</span>
            </div>
            <div class="status-item">
                <span>🔧 存储模式：</span>
                <span id="db-mode">--</span>
            </div>
            <div class="status-item">
                <span>📈 记录总数：</span>
                <span id="record-count">0</span>
            </div>
            <div class="status-item">
                <span>⏰ 最后更新：</span>
                <span id="last-update">--</span>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        // 全局变量
        let trades = [];
        let statistics = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('页面加载完成，开始初始化...');

            // 加载交易默认设置
            await loadTradingDefaults();

            // 加载交易记录
            await loadTrades();

            // 加载统计信息
            await loadStatistics();

            // 更新数据库状态
            await updateDbStatus();

            // 初始化快速录入界面
            initializeQuickAdd();

            console.log('初始化完成');
        });

        // 初始化快速录入界面
        function initializeQuickAdd() {
            // 设置默认时间
            setCurrentTime('entry-time');
            setCurrentTime('exit-time');

            // 设置默认值
            document.getElementById('position-size').value = '10000';
            document.getElementById('result-pct').value = '1.0';
            document.getElementById('entry-fee').value = '0.05';
            document.getElementById('exit-fee').value = '0.05';

            // 初始计算
            updateCalculations();
        }

        // 加载交易默认设置
        async function loadTradingDefaults() {
            try {
                const result = await ipcRenderer.invoke('get-trading-defaults');
                if (result.success) {
                    const defaults = result.defaults;
                    document.getElementById('position-size').value = defaults.position_size;
                    document.getElementById('entry-fee').value = defaults.entry_fee_pct;
                    document.getElementById('exit-fee').value = defaults.exit_fee_pct;
                }
            } catch (error) {
                console.error('加载默认设置失败:', error);
            }
        }

        // 快速选择仓位大小
        function selectPositionSize(button, value) {
            // 更新按钮状态
            document.querySelectorAll('.form-section:first-child .quick-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            button.classList.add('selected');

            // 设置输入框值
            document.getElementById('position-size').value = value;

            // 更新计算
            updateCalculations();
        }

        // 快速选择交易结果
        function selectResult(button, value) {
            // 更新按钮状态
            button.parentElement.querySelectorAll('.quick-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            button.classList.add('selected');

            // 设置输入框值
            document.getElementById('result-pct').value = value;

            // 更新计算
            updateCalculations();
        }

        // 快速选择开仓手续费
        function selectEntryFee(button, value) {
            // 更新按钮状态
            button.parentElement.querySelectorAll('.quick-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            button.classList.add('selected');

            // 设置输入框值
            document.getElementById('entry-fee').value = value;

            // 更新计算
            updateCalculations();
        }

        // 快速选择平仓手续费
        function selectExitFee(button, value) {
            // 更新按钮状态
            button.parentElement.querySelectorAll('.quick-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            button.classList.add('selected');

            // 设置输入框值
            document.getElementById('exit-fee').value = value;

            // 更新计算
            updateCalculations();
        }

        // 实时计算更新
        function updateCalculations() {
            const positionSize = parseFloat(document.getElementById('position-size').value) || 0;
            const resultPct = parseFloat(document.getElementById('result-pct').value) || 0;
            const entryFeePct = parseFloat(document.getElementById('entry-fee').value) || 0.05;
            const exitFeePct = parseFloat(document.getElementById('exit-fee').value) || 0.05;

            // 计算毛收益
            const grossProfit = positionSize * (resultPct / 100);

            // 计算手续费
            const entryFees = positionSize * (entryFeePct / 100);
            const exitFees = positionSize * (exitFeePct / 100);
            const totalFees = entryFees + exitFees;

            // 计算净收益
            const netProfit = grossProfit - totalFees;

            // 计算净收益率
            const netReturnRate = positionSize > 0 ? (netProfit / positionSize) * 100 : 0;

            // 更新显示
            document.getElementById('gross-profit').textContent = formatCurrency(grossProfit);
            document.getElementById('entry-fees').textContent = formatCurrency(-entryFees);
            document.getElementById('exit-fees').textContent = formatCurrency(-exitFees);
            document.getElementById('total-fees').textContent = formatCurrency(-totalFees);
            document.getElementById('net-profit').textContent = formatCurrency(netProfit);
            document.getElementById('return-rate').textContent = `${netReturnRate >= 0 ? '+' : ''}${netReturnRate.toFixed(2)}%`;

            // 更新颜色
            updateCalculationColors(grossProfit, netProfit, netReturnRate);

            // 更新持仓时间
            updateDuration();
        }

        // 更新计算结果颜色
        function updateCalculationColors(grossProfit, netProfit, netReturnRate) {
            const grossElement = document.getElementById('gross-profit');
            const netElement = document.getElementById('net-profit');
            const rateElement = document.getElementById('return-rate');

            // 毛收益颜色
            grossElement.style.color = grossProfit >= 0 ? '#00ff88' : '#ff4757';

            // 净收益颜色
            netElement.style.color = netProfit >= 0 ? '#00ff88' : '#ff4757';

            // 收益率颜色
            rateElement.style.color = netReturnRate >= 0 ? '#00ff88' : '#ff4757';
        }

        // 更新持仓时间显示
        function updateDuration() {
            const entryTime = document.getElementById('entry-time').value;
            const exitTime = document.getElementById('exit-time').value;

            if (entryTime && exitTime) {
                const entry = new Date(entryTime);
                const exit = new Date(exitTime);
                const durationMs = exit.getTime() - entry.getTime();
                const durationMinutes = Math.round(durationMs / (1000 * 60));

                if (durationMinutes >= 0) {
                    document.getElementById('duration-display').textContent = `${durationMinutes} 分钟`;
                } else {
                    document.getElementById('duration-display').textContent = '时间错误';
                }
            } else {
                document.getElementById('duration-display').textContent = '-- 分钟';
            }
        }

        // 加载交易记录
        async function loadTrades() {
            try {
                showLoading(true);

                const result = await ipcRenderer.invoke('get-trades', {
                    limit: 50,
                    offset: 0,
                    orderBy: 'created_at',
                    orderDirection: 'DESC'
                });

                if (result.success) {
                    trades = result.trades;
                    renderTrades();
                    updateRecordCount(result.total);
                } else {
                    showMessage(`加载交易记录失败：${result.error}`, 'error');
                }
            } catch (error) {
                console.error('加载交易记录失败:', error);
                showMessage(`加载交易记录失败：${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const result = await ipcRenderer.invoke('get-trade-statistics');

                if (result.success) {
                    statistics = result.statistics;
                    renderStatistics();
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 渲染交易记录
        function renderTrades() {
            const container = document.getElementById('trades-list');

            if (trades.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">暂无交易记录</div>';
                return;
            }

            container.innerHTML = trades.map(trade => `
                <div class="trade-item">
                    <div class="trade-info">
                        <div class="trade-field id-field">
                            <div class="label">ID</div>
                            <div class="value">#${trade.id}</div>
                        </div>
                        <div class="trade-field">
                            <div class="label">仓位</div>
                            <div class="value">¥${formatNumber(trade.position_size)}</div>
                        </div>
                        <div class="trade-field">
                            <div class="label">方向</div>
                            <div class="value ${trade.direction === 'Long' ? 'direction-long' : 'direction-short'}">
                                ${trade.direction === 'Long' ? '做多' : '做空'}
                            </div>
                        </div>
                        <div class="trade-field">
                            <div class="label">收益率</div>
                            <div class="value ${trade.result_pct >= 0 ? 'profit' : 'loss'}">
                                ${trade.result_pct >= 0 ? '+' : ''}${trade.result_pct}%
                            </div>
                        </div>
                        <div class="trade-field">
                            <div class="label">净盈亏</div>
                            <div class="value ${trade.net_pnl >= 0 ? 'profit' : 'loss'}">
                                ¥${formatNumber(trade.net_pnl)}
                            </div>
                        </div>
                        <div class="trade-field">
                            <div class="label">手续费</div>
                            <div class="value">¥${formatNumber(trade.total_fees)}</div>
                        </div>
                        <div class="trade-field">
                            <div class="label">持仓时间</div>
                            <div class="value">${formatDuration(trade.duration_minutes)}</div>
                        </div>
                        <div class="trade-field">
                            <div class="label">品种</div>
                            <div class="value">${trade.instrument || '-'}</div>
                        </div>
                    </div>
                    <div class="trade-actions">
                        <button class="btn-delete" onclick="deleteTrade(${trade.id})" title="删除交易记录">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 渲染统计信息
        function renderStatistics() {
            document.getElementById('total-trades').textContent = statistics.total_trades || 0;
            document.getElementById('win-rate').textContent = `${(statistics.win_rate || 0).toFixed(1)}%`;
            document.getElementById('total-pnl').textContent = `¥${formatNumber(statistics.total_pnl || 0)}`;
            document.getElementById('avg-pnl').textContent = `¥${formatNumber(statistics.avg_pnl || 0)}`;
        }

        // 删除交易记录
        async function deleteTrade(tradeId) {
            if (!confirm('确定要删除这条交易记录吗？')) {
                return;
            }

            try {
                const result = await ipcRenderer.invoke('delete-trade', tradeId);

                if (result.success) {
                    showMessage('交易记录删除成功！', 'success');
                    await loadTrades();
                    await loadStatistics();
                    await updateDbStatus();
                } else {
                    showMessage(`删除失败：${result.error}`, 'error');
                }
            } catch (error) {
                console.error('删除交易记录失败:', error);
                showMessage(`删除失败：${error.message}`, 'error');
            }
        }

        // 更新数据库状态
        async function updateDbStatus() {
            try {
                const result = await ipcRenderer.invoke('get-db-stats');

                if (result.success) {
                    document.getElementById('db-status').textContent = result.isConnected ? '已连接' : '连接失败';

                    // 显示数据库模式
                    let modeText, modeColor;

                    if (result.mode === 'sqlite') {
                        modeText = 'SQLite数据库';
                        modeColor = '#28a745';
                    } else if (result.mode === 'memory') {
                        modeText = '内存模式';
                        modeColor = '#ffc107';

                        // 如果有初始化错误，显示提示
                        if (result.initError) {
                            modeText += ' (SQLite失败)';
                            console.warn('SQLite初始化失败原因:', result.initError);
                        }
                    } else {
                        modeText = '错误';
                        modeColor = '#dc3545';
                    }

                    const modeElement = document.getElementById('db-mode');
                    modeElement.textContent = modeText;
                    modeElement.style.color = modeColor;
                    modeElement.style.fontWeight = 'bold';

                    document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
                } else {
                    document.getElementById('db-status').textContent = '连接失败';
                    document.getElementById('db-mode').textContent = '错误';
                    document.getElementById('db-mode').style.color = '#dc3545';
                }
            } catch (error) {
                document.getElementById('db-status').textContent = '连接错误';
                document.getElementById('db-mode').textContent = '错误';
                document.getElementById('db-mode').style.color = '#dc3545';
                console.error('更新数据库状态失败:', error);
            }
        }

        // 更新记录总数
        function updateRecordCount(count) {
            document.getElementById('record-count').textContent = count;
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';

            messageArea.innerHTML = `<div class="${className}">${message}</div>`;

            // 3秒后自动清除消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 保存交易记录
        async function saveTrade() {
            const formData = getFormData();

            if (!validateFormData(formData)) {
                return;
            }

            try {
                showMessage('正在保存交易记录...', 'info');

                const result = await ipcRenderer.invoke('add-trade', formData);

                if (result.success) {
                    showMessage('交易记录保存成功！', 'success');

                    // 刷新数据
                    await loadTrades();
                    await loadStatistics();
                    await updateDbStatus();
                } else {
                    showMessage(`保存失败：${result.error}`, 'error');
                }
            } catch (error) {
                console.error('保存交易记录失败:', error);
                showMessage(`保存失败：${error.message}`, 'error');
            }
        }

        // 保存并继续
        async function saveAndContinue() {
            await saveTrade();

            // 清空部分字段，保留常用设置
            document.getElementById('result-pct').value = '';
            document.getElementById('notes').value = '';
            document.getElementById('instrument').value = '';

            // 重新设置时间
            setCurrentTime('entry-time');
            setCurrentTime('exit-time');

            // 更新计算
            updateCalculations();

            showMessage('已清空部分字段，可以继续录入下一笔交易', 'info');
        }

        // 复制上一笔交易
        async function copyLastTrade() {
            if (trades.length === 0) {
                showMessage('没有可复制的交易记录', 'error');
                return;
            }

            const lastTrade = trades[0]; // 最新的交易记录

            // 填充表单
            document.getElementById('position-size').value = lastTrade.position_size;
            document.getElementById('result-pct').value = lastTrade.result_pct;
            document.getElementById('entry-fee').value = lastTrade.entry_fee_pct;
            document.getElementById('exit-fee').value = lastTrade.exit_fee_pct;
            document.getElementById('instrument').value = lastTrade.instrument || '';

            // 设置交易方向
            const directionRadio = document.querySelector(`input[name="direction"][value="${lastTrade.direction}"]`);
            if (directionRadio) {
                directionRadio.checked = true;
            }

            // 更新按钮状态
            updateButtonStates();

            // 重新设置时间为当前时间
            setCurrentTime('entry-time');
            setCurrentTime('exit-time');

            // 更新计算
            updateCalculations();

            showMessage('已复制上一笔交易的设置', 'success');
        }

        // 使用模板（简化版）
        function useTemplate() {
            showMessage('模板功能将在后续版本中实现', 'info');
        }

        // 获取表单数据
        function getFormData() {
            const direction = document.querySelector('input[name="direction"]:checked')?.value;

            return {
                position_size: parseFloat(document.getElementById('position-size').value),
                direction: direction,
                result_pct: parseFloat(document.getElementById('result-pct').value),
                entry_time: document.getElementById('entry-time').value,
                exit_time: document.getElementById('exit-time').value,
                entry_fee_pct: parseFloat(document.getElementById('entry-fee').value) || 0.05,
                exit_fee_pct: parseFloat(document.getElementById('exit-fee').value) || 0.05,
                instrument: document.getElementById('instrument').value || '',
                notes: document.getElementById('notes').value || ''
            };
        }

        // 验证表单数据
        function validateFormData(data) {
            if (!data.position_size || data.position_size <= 0) {
                showMessage('请输入有效的仓位大小', 'error');
                return false;
            }

            if (!data.direction) {
                showMessage('请选择交易方向', 'error');
                return false;
            }

            if (data.result_pct === null || data.result_pct === undefined || isNaN(data.result_pct)) {
                showMessage('请输入有效的收益率', 'error');
                return false;
            }

            if (!data.entry_time) {
                showMessage('请选择开仓时间', 'error');
                return false;
            }

            if (!data.exit_time) {
                showMessage('请选择平仓时间', 'error');
                return false;
            }

            // 检查时间顺序
            if (new Date(data.entry_time) >= new Date(data.exit_time)) {
                showMessage('平仓时间必须晚于开仓时间', 'error');
                return false;
            }

            return true;
        }

        // 更新按钮状态
        function updateButtonStates() {
            const positionSize = document.getElementById('position-size').value;
            const resultPct = document.getElementById('result-pct').value;
            const entryFee = document.getElementById('entry-fee').value;
            const exitFee = document.getElementById('exit-fee').value;

            // 更新仓位按钮状态
            document.querySelectorAll('.form-section:first-child .quick-button').forEach(btn => {
                btn.classList.remove('selected');
                if (btn.textContent.includes('1万') && positionSize == '10000') btn.classList.add('selected');
                if (btn.textContent.includes('5千') && positionSize == '5000') btn.classList.add('selected');
                if (btn.textContent.includes('2万') && positionSize == '20000') btn.classList.add('selected');
                if (btn.textContent.includes('5万') && positionSize == '50000') btn.classList.add('selected');
            });

            // 更新收益率按钮状态
            document.querySelectorAll('.form-section:nth-child(3) .quick-button').forEach(btn => {
                btn.classList.remove('selected');
                const btnValue = btn.onclick.toString().match(/selectResult\(this, '([^']+)'\)/)?.[1];
                if (btnValue && resultPct == btnValue) btn.classList.add('selected');
            });

            // 更新手续费按钮状态
            document.querySelectorAll('.form-section:nth-child(5) .quick-button').forEach(btn => {
                btn.classList.remove('selected');
                const btnValue = btn.onclick.toString().match(/select\w+Fee\(this, '([^']+)'\)/)?.[1];
                if (btnValue && (entryFee == btnValue || exitFee == btnValue)) btn.classList.add('selected');
            });
        }

        // 格式化货币显示
        function formatCurrency(amount) {
            const absAmount = Math.abs(amount);
            const sign = amount >= 0 ? '+' : '-';

            if (absAmount >= 10000) {
                return `${sign}${(absAmount / 10000).toFixed(2)}万元`;
            } else if (absAmount >= 1000) {
                return `${sign}${(absAmount / 1000).toFixed(2)}千元`;
            } else {
                return `${sign}${absAmount.toFixed(2)}元`;
            }
        }

        // 设置当前时间
        function setCurrentTime(inputId) {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                .toISOString()
                .slice(0, 16);
            document.getElementById(inputId).value = localDateTime;

            // 如果设置的是开仓时间，自动设置平仓时间为1小时后
            if (inputId === 'entry-time') {
                const exitTime = new Date(now.getTime() + 60 * 60 * 1000 - now.getTimezoneOffset() * 60000)
                    .toISOString()
                    .slice(0, 16);
                document.getElementById('exit-time').value = exitTime;
            }

            updateCalculations();
        }

        // 格式化数字
        function formatNumber(num) {
            if (num === null || num === undefined) return '0';
            return parseFloat(num).toFixed(2);
        }

        // 格式化持仓时间
        function formatDuration(minutes) {
            if (!minutes) return '-';

            const hours = Math.floor(minutes / 60);
            const mins = Math.floor(minutes % 60);

            if (hours > 0) {
                return `${hours}h ${mins}m`;
            } else {
                return `${mins}m`;
            }
        }

        console.log('交易复盘工具前端已加载 - 第三步：快速录入功能');
    </script>
</body>
</html>