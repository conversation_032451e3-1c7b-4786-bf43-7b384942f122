const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// 导入数据库管理器和DAO
const dbManager = require('../database/db-manager-safe');
const tradeDAO = require('../dao/trade-dao-safe');
const preferencesDAO = require('../dao/preferences-dao-safe');

let mainWindow;

// 数据库路径
const DB_PATH = path.join(__dirname, '../../data/trading.db');

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1600,
        height: 1000,
        minWidth: 1400,
        minHeight: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });

    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));

    // 开发模式下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

// 应用程序初始化
app.whenReady().then(async () => {
    try {
        // 初始化数据库
        await dbManager.initialize(DB_PATH);
        console.log('数据库初始化成功');

        // 创建窗口
        createWindow();

        // macOS 特殊处理
        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                createWindow();
            }
        });

    } catch (error) {
        console.error('应用初始化失败:', error);
        app.quit();
    }
});

// 应用程序退出处理
app.on('window-all-closed', () => {
    // 关闭数据库连接
    dbManager.close();

    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// IPC 处理器 - 交易记录相关

// 添加交易记录
ipcMain.handle('add-trade', async (event, tradeData) => {
    try {
        console.log('添加交易记录:', tradeData);

        // 创建交易记录
        const newTrade = await tradeDAO.create(tradeData);

        // 更新最近使用的仓位大小
        await preferencesDAO.updateRecentPositionSizes(tradeData.position_size);

        return { success: true, trade: newTrade };
    } catch (error) {
        console.error('添加交易记录失败:', error);
        return { success: false, error: error.message };
    }
});

// 获取交易记录列表
ipcMain.handle('get-trades', async (event, options = {}) => {
    try {
        console.log('获取交易记录列表:', options);

        const trades = await tradeDAO.findAll(options);
        const total = await tradeDAO.count(options.filters || {});

        return {
            success: true,
            trades,
            total,
            pagination: {
                limit: options.limit || 100,
                offset: options.offset || 0,
                total
            }
        };
    } catch (error) {
        console.error('获取交易记录失败:', error);
        return { success: false, error: error.message };
    }
});

// 删除交易记录
ipcMain.handle('delete-trade', async (event, tradeId) => {
    try {
        console.log('删除交易记录:', tradeId);

        const result = await tradeDAO.delete(tradeId);
        return { success: true, result };
    } catch (error) {
        console.error('删除交易记录失败:', error);
        return { success: false, error: error.message };
    }
});

// 更新交易记录
ipcMain.handle('update-trade', async (event, tradeId, updateData) => {
    try {
        console.log('更新交易记录:', tradeId, updateData);

        const updatedTrade = await tradeDAO.update(tradeId, updateData);
        return { success: true, trade: updatedTrade };
    } catch (error) {
        console.error('更新交易记录失败:', error);
        return { success: false, error: error.message };
    }
});

// 获取交易统计
ipcMain.handle('get-trade-statistics', async (event, filters = {}) => {
    try {
        console.log('获取交易统计:', filters);

        const statistics = await tradeDAO.getStatistics(filters);
        return { success: true, statistics };
    } catch (error) {
        console.error('获取交易统计失败:', error);
        return { success: false, error: error.message };
    }
});

// IPC 处理器 - 用户偏好设置相关

// 获取用户偏好设置
ipcMain.handle('get-preference', async (event, key) => {
    try {
        const value = await preferencesDAO.get(key);
        return { success: true, value };
    } catch (error) {
        console.error('获取用户偏好设置失败:', error);
        return { success: false, error: error.message };
    }
});

// 设置用户偏好
ipcMain.handle('set-preference', async (event, key, value, description, category) => {
    try {
        const result = await preferencesDAO.set(key, value, description, category);
        return { success: true, result };
    } catch (error) {
        console.error('设置用户偏好失败:', error);
        return { success: false, error: error.message };
    }
});

// 获取交易默认设置
ipcMain.handle('get-trading-defaults', async (event) => {
    try {
        const defaults = await preferencesDAO.getTradingDefaults();
        return { success: true, defaults };
    } catch (error) {
        console.error('获取交易默认设置失败:', error);
        return { success: false, error: error.message };
    }
});

// IPC 处理器 - 数据库管理相关

// 获取数据库统计信息
ipcMain.handle('get-db-stats', async (event) => {
    try {
        const stats = await dbManager.getStats();
        const mode = dbManager.getMode();
        const isConnected = dbManager.isConnected();
        const initError = dbManager.getInitError();

        return {
            success: true,
            stats,
            mode,
            isConnected,
            initError
        };
    } catch (error) {
        console.error('获取数据库统计失败:', error);
        return { success: false, error: error.message };
    }
});

// 备份数据库
ipcMain.handle('backup-database', async (event, backupPath) => {
    try {
        await dbManager.backup(backupPath);
        return { success: true };
    } catch (error) {
        console.error('备份数据库失败:', error);
        return { success: false, error: error.message };
    }
});

// 优化数据库
ipcMain.handle('optimize-database', async (event) => {
    try {
        await dbManager.optimize();
        return { success: true };
    } catch (error) {
        console.error('优化数据库失败:', error);
        return { success: false, error: error.message };
    }
});

// 获取交易品种历史
ipcMain.handle('get-instrument-history', async (event) => {
    try {
        const instruments = await dbManager.getInstrumentHistory();
        return { success: true, instruments };
    } catch (error) {
        console.error('获取交易品种历史失败:', error);
        return { success: false, error: error.message };
    }
});

// 保存交易品种历史
ipcMain.handle('save-instrument-history', async (event, instruments) => {
    try {
        await dbManager.saveInstrumentHistory(instruments);
        return { success: true };
    } catch (error) {
        console.error('保存交易品种历史失败:', error);
        return { success: false, error: error.message };
    }
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    // 确保数据库连接关闭
    dbManager.close();
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});

console.log('交易复盘工具主进程已启动 - 第二步：完整数据模型');