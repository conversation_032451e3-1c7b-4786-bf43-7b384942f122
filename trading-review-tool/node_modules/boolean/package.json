{"name": "boolean", "version": "3.2.0", "description": "boolean converts lots of things to boolean.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "private": false, "main": "build/lib/index.js", "types": "build/lib/index.d.ts", "dependencies": {}, "devDependencies": {"assertthat": "6.4.0", "roboter": "12.7.0", "semantic-release-configuration": "2.0.7"}, "scripts": {}, "repository": {"type": "git", "url": "git://github.com/thenativeweb/boolean.git"}, "keywords": ["boolean", "parser"], "license": "MIT"}