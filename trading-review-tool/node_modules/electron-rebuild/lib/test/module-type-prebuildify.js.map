{"version": 3, "file": "module-type-prebuildify.js", "sourceRoot": "", "sources": ["../../test/module-type-prebuildify.ts"], "names": [], "mappings": ";;;;;AAAA,mCAAsC;AACtC,+BAA8B;AAC9B,gDAAwB;AAExB,gEAIwC;AACxC,4CAA6D;AAE7D,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAA,aAAM,EAAC,IAAA,yCAA2B,EAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,IAAA,aAAM,EAAC,IAAA,yCAA2B,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;IAChD,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;QAChD,IAAA,aAAM,EAAC,IAAA,8CAAgC,EAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,IAAA,aAAM,EAAC,IAAA,8CAAgC,EAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;QAC9C,IAAA,aAAM,EAAC,IAAA,8CAAgC,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IACtE,MAAM,aAAa,GAAG;QACpB,SAAS,EAAE,kBAAkB;QAC7B,eAAe,EAAE,QAAQ;QACzB,IAAI,EAAE,KAAK;QACX,SAAS,EAAE,IAAI,qBAAY,EAAE;KAC9B,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,OAAkC,EAAE,EAAa,EAAE;QAC1E,MAAM,SAAS,GAAG,IAAI,mBAAS,CAAC,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,EAAC,CAAC,CAAC;QAC9D,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC7B,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;IAEF,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,wBAAwB,CAAC,CAAC,CAAC;YACpG,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC,CAAC;YACnG,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;YAC3C,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,MAAM,cAAc,GAAG,SAAS,CAAC;gBACjC,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBAC/D,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YAC5D,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;gBACrE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YACnD,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;gBACjE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;gBACpE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,eAAe,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;gBACtD,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;gBACvE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,eAAe,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;gBACrD,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YAC3D,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;gBAC1D,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}