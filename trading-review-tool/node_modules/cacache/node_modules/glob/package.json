{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "name": "glob", "description": "a little globber", "version": "8.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "main": "glob.js", "files": ["glob.js", "sync.js", "common.js"], "engines": {"node": ">=12"}, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "devDependencies": {"memfs": "^3.2.0", "mkdirp": "0", "rimraf": "^2.2.8", "tap": "^16.0.1", "tick": "0.0.6"}, "tap": {"before": "test/00-setup.js", "after": "test/zz-cleanup.js", "statements": 90, "branches": 90, "functions": 90, "lines": 90, "jobs": 1}, "scripts": {"prepublish": "npm run benchclean", "profclean": "rm -f v8.log profile.txt", "test": "tap", "test-regen": "npm run profclean && TEST_REGEN=1 node test/00-setup.js", "bench": "bash benchmark.sh", "prof": "bash prof.sh && cat profile.txt", "benchclean": "node benchclean.js"}, "license": "ISC", "funding": {"url": "https://github.com/sponsors/isaacs"}}